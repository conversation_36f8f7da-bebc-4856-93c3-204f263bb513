<template>
  <div class="thinking-message">
    <div class="thinking-header">
      <div class="thinking-icon">💭</div>
      <div class="thinking-title">思考中...</div>
      <div class="thinking-timestamp">{{ formatTime(timestamp) }}</div>
    </div>
    
    <div v-if="content" class="thinking-content">
      <div class="thinking-text" v-html="formattedContent"></div>
    </div>
    
    <!-- 思考动画 -->
    <div v-if="isActive" class="thinking-animation">
      <div class="thinking-dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { parseMarkdown } from '../utils/markdown'

// Props
interface Props {
  content: string
  timestamp: number
  isActive?: boolean // 是否正在思考中
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
})

/**
 * 格式化思考内容
 */
const formattedContent = computed(() => {
  if (!props.content) return ''
  
  // 移除开头的 "💭 Thinking..." 如果存在
  let content = props.content.replace(/^💭\s*Thinking\.\.\./, '').trim()
  
  if (!content) return ''
  
  return parseMarkdown(content)
})

/**
 * 格式化时间显示
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.thinking-message {
  margin: 8px 0;
  border: 1px solid rgba(147, 51, 234, 0.3);
  border-radius: 8px;
  background: linear-gradient(135deg, 
    rgba(147, 51, 234, 0.05) 0%, 
    rgba(147, 51, 234, 0.02) 100%);
  overflow: hidden;
  position: relative;
}

.thinking-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(147, 51, 234, 0.6) 0%, 
    rgba(168, 85, 247, 0.6) 50%, 
    rgba(147, 51, 234, 0.6) 100%);
  animation: thinking-glow 2s ease-in-out infinite;
}

@keyframes thinking-glow {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(147, 51, 234, 0.08);
}

.thinking-icon {
  font-size: 16px;
  margin-right: 8px;
  animation: thinking-bounce 1.5s ease-in-out infinite;
}

@keyframes thinking-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.thinking-title {
  flex: 1;
  font-weight: 600;
  color: var(--vscode-foreground);
  font-size: 13px;
}

.thinking-timestamp {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  opacity: 0.7;
}

.thinking-content {
  padding: 12px;
  background: var(--vscode-editor-background);
}

.thinking-text {
  font-size: 13px;
  line-height: 1.5;
  color: var(--vscode-foreground);
  opacity: 0.9;
}

/* Markdown样式 */
.thinking-text :deep(p) {
  margin: 0 0 8px 0;
}

.thinking-text :deep(p:last-child) {
  margin-bottom: 0;
}

.thinking-text :deep(code) {
  background: var(--vscode-textCodeBlock-background);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
  font-size: 12px;
}

.thinking-text :deep(pre) {
  background: var(--vscode-textCodeBlock-background);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
}

.thinking-text :deep(pre code) {
  background: none;
  padding: 0;
}

.thinking-text :deep(ul), 
.thinking-text :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.thinking-text :deep(li) {
  margin: 4px 0;
}

.thinking-text :deep(blockquote) {
  border-left: 3px solid rgba(147, 51, 234, 0.5);
  padding-left: 12px;
  margin: 8px 0;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

.thinking-animation {
  padding: 8px 12px;
  background: rgba(147, 51, 234, 0.05);
  border-top: 1px solid rgba(147, 51, 234, 0.2);
}

.thinking-dots {
  display: flex;
  align-items: center;
  gap: 6px;
}

.thinking-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(147, 51, 234, 0.7);
  animation: thinking-pulse 1.4s ease-in-out infinite both;
}

.thinking-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.thinking-dots .dot:nth-child(3) { animation-delay: 0s; }

@keyframes thinking-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .thinking-message {
    border-color: rgba(168, 85, 247, 0.4);
    background: linear-gradient(135deg, 
      rgba(168, 85, 247, 0.08) 0%, 
      rgba(168, 85, 247, 0.03) 100%);
  }
  
  .thinking-header {
    background: rgba(168, 85, 247, 0.12);
  }
  
  .thinking-animation {
    background: rgba(168, 85, 247, 0.08);
    border-top-color: rgba(168, 85, 247, 0.3);
  }
  
  .thinking-dots .dot {
    background: rgba(168, 85, 247, 0.8);
  }
  
  .thinking-text :deep(blockquote) {
    border-left-color: rgba(168, 85, 247, 0.6);
  }
}
</style>
