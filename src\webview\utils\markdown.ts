import MarkdownIt from 'markdown-it'

/**
 * Markdown解析器配置
 */
const md = new MarkdownIt({
  // 启用HTML标签
  html: true,
  // 自动转换链接
  linkify: true,
  // 启用排版增强
  typographer: true,
  // 启用换行转换
  breaks: true
})

/**
 * 自定义渲染规则
 */
// 自定义代码块渲染
md.renderer.rules.fence = (tokens, idx, options, env, renderer) => {
  const token = tokens[idx]
  const info = token.info ? token.info.trim() : ''
  const langName = info ? info.split(/\s+/g)[0] : ''
  const langClass = langName ? ` language-${langName}` : ''
  
  // 生成唯一ID用于复制功能
  const codeId = `code_${Math.random().toString(36).substr(2, 9)}`
  
  return `<div class="code-block-container">
    <div class="code-block-header">
      <span class="code-block-language">${langName || 'text'}</span>
      <button class="code-copy-btn" onclick="copyCodeToClipboard('${codeId}')" title="复制代码">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
        </svg>
      </button>
    </div>
    <pre class="code-block${langClass}"><code id="${codeId}" data-lang="${langName}">${md.utils.escapeHtml(token.content)}</code></pre>
  </div>`
}

// 自定义行内代码渲染
md.renderer.rules.code_inline = (tokens, idx, options, env, renderer) => {
  const token = tokens[idx]
  return `<code class="inline-code">${md.utils.escapeHtml(token.content)}</code>`
}

// 自定义链接渲染（在新窗口打开）
md.renderer.rules.link_open = (tokens, idx, options, env, renderer) => {
  const token = tokens[idx]
  const href = token.attrGet('href')
  
  // 为外部链接添加target="_blank"
  if (href && (href.startsWith('http://') || href.startsWith('https://'))) {
    token.attrSet('target', '_blank')
    token.attrSet('rel', 'noopener noreferrer')
  }
  
  return renderer.renderToken(tokens, idx, options)
}

// 自定义表格渲染
md.renderer.rules.table_open = () => '<div class="table-container"><table class="markdown-table">'
md.renderer.rules.table_close = () => '</table></div>'

/**
 * 解析Markdown文本为HTML
 */
export function parseMarkdown(markdown: string): string {
  if (!markdown || typeof markdown !== 'string') {
    return ''
  }

  return md.render(markdown)
}

/**
 * 复制代码到剪贴板的全局函数
 * 需要在window对象上暴露
 */
export function setupCodeCopyFunction() {
  // 在window对象上添加复制函数
  ;(window as any).copyCodeToClipboard = async (codeId: string) => {
    try {
      const codeElement = document.getElementById(codeId)
      if (!codeElement) {
        console.error('找不到代码元素:', codeId)
        return
      }
      
      const code = codeElement.textContent || ''
      
      // 使用现代剪贴板API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(code)
      } else {
        // 降级方案：使用传统方法
        const textArea = document.createElement('textarea')
        textArea.value = code
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        textArea.remove()
      }
      
      // 显示复制成功提示
      showCopySuccess(codeId)
    } catch (error) {
      console.error('复制失败:', error)
      showCopyError(codeId)
    }
  }
}

/**
 * 显示复制成功提示
 */
function showCopySuccess(codeId: string) {
  const button = document.querySelector(`button[onclick="copyCodeToClipboard('${codeId}')"]`)
  if (button) {
    const originalTitle = button.getAttribute('title')
    button.setAttribute('title', '复制成功！')
    button.classList.add('copy-success')
    
    setTimeout(() => {
      button.setAttribute('title', originalTitle || '复制代码')
      button.classList.remove('copy-success')
    }, 2000)
  }
}

/**
 * 显示复制错误提示
 */
function showCopyError(codeId: string) {
  const button = document.querySelector(`button[onclick="copyCodeToClipboard('${codeId}')"]`)
  if (button) {
    const originalTitle = button.getAttribute('title')
    button.setAttribute('title', '复制失败')
    button.classList.add('copy-error')
    
    setTimeout(() => {
      button.setAttribute('title', originalTitle || '复制代码')
      button.classList.remove('copy-error')
    }, 2000)
  }
}

/**
 * 获取markdown-it实例（用于扩展）
 */
export function getMarkdownInstance(): MarkdownIt {
  return md
}
