/**
 * Claude Code 类型定义
 */

// 消息类型定义
export interface Message {
  id: string
  type: 'user' | 'assistant' | 'system' | 'tool' | 'thinking' | 'error'
  content: string
  timestamp: number
  isStreaming?: boolean
  // 工具调用相关
  toolInfo?: string
  toolInput?: any
  rawInput?: any
  toolName?: string
  toolUseId?: string
  isError?: boolean
  hidden?: boolean
}

// VSCode Webview消息类型
export interface WebviewMessage {
  type: 'sendMessage' | 'newSession' | 'clearChat' | 'stopRequest'
  data?: any
}

// 扩展到Webview的消息类型
export interface ExtensionMessage {
  type: 'message' | 'streamingMessage' | 'streamingStart' | 'streamingComplete' | 'error' | 'sessionInfo' |
        'toolUse' | 'toolResult' | 'thinking' | 'loading' | 'setProcessing' | 'clearLoading' |
        'updateTokens' | 'updateTotals' | 'statusUpdate'
  data?: any
}

// 聊天状态
export interface ChatState {
  messages: Message[]
  isLoading: boolean
  sessionId?: string
  // 连接状态
  connectionStatus: 'ready' | 'processing' | 'error'
  // 统计信息
  totalCost: number
  totalTokensInput: number
  totalTokensOutput: number
  requestCount: number
  // 当前请求信息
  isProcessing: boolean
  requestStartTime?: number
}

// 状态更新数据
export interface StatusUpdateData {
  connectionStatus?: 'ready' | 'processing' | 'error'
  statusText?: string
  totalCost?: number
  totalTokensInput?: number
  totalTokensOutput?: number
  requestCount?: number
  currentCost?: number
  currentDuration?: number
  isProcessing?: boolean
  requestStartTime?: number
}

// 工具调用数据
export interface ToolUseData {
  toolInfo: string
  toolInput: any
  rawInput: any
  toolName: string
  toolUseId?: string
}

// 工具结果数据
export interface ToolResultData {
  content: string
  isError: boolean
  toolUseId: string
  toolName: string
  hidden?: boolean
}

// VSCode API类型声明
declare global {
  interface Window {
    acquireVsCodeApi(): {
      postMessage(message: WebviewMessage): void
      setState(state: any): void
      getState(): any
    }
  }
}
