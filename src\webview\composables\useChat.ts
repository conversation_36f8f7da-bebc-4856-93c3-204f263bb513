import { ref, reactive, nextTick } from 'vue'
import type { Message, ChatState, WebviewMessage, ExtensionMessage } from '../../types'
import { vscodeAPI } from '../utils/vscode'

/**
 * 聊天功能组合式函数
 */
export function useChat() {
  // 聊天状态
  const chatState = reactive<ChatState>({
    messages: [],
    isLoading: false,
    sessionId: undefined,
    connectionStatus: 'ready',
    totalCost: 0,
    totalTokensInput: 0,
    totalTokensOutput: 0,
    requestCount: 0,
    isProcessing: false,
    requestStartTime: undefined
  })

  // 当前输入内容
  const currentInput = ref('')
  
  // VSCode API 已在 utils/vscode.ts 中统一管理

  /**
   * 生成消息ID
   */
  const generateMessageId = (): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 添加消息到聊天记录
   */
  const addMessage = (message: Omit<Message, 'id'>): Message => {
    const newMessage: Message = {
      id: generateMessageId(),
      ...message
    }
    chatState.messages.push(newMessage)
    return newMessage
  }



  /**
   * 发送消息给Claude
   */
  const sendMessage = async (content: string) => {
    if (!content.trim() || chatState.isLoading) return

    // 添加用户消息
    addMessage({
      type: 'user',
      content: content.trim(),
      timestamp: Date.now()
    })



    // 清空输入框
    currentInput.value = ''

    // 设置加载状态
    chatState.isLoading = true
    chatState.connectionStatus = 'processing'
    chatState.isProcessing = true
    chatState.requestStartTime = Date.now()

    // 发送消息到扩展
    const message: WebviewMessage = {
      type: 'sendMessage',
      data: { content: content.trim() }
    }
    vscodeAPI.postMessage(message)

    // 发送消息后强制滚动到底部
    await nextTick()
    scrollToBottom(true)
  }

  /**
   * 停止当前请求
   */
  const stopRequest = () => {
    const message: WebviewMessage = {
      type: 'stopRequest'
    }
    vscodeAPI.postMessage(message)

    // 立即更新状态
    chatState.isLoading = false
    chatState.connectionStatus = 'ready'
    chatState.isProcessing = false
    chatState.requestStartTime = undefined

    // 确保最后一条流式消息被正确标记为完成
    const lastMessage = chatState.messages[chatState.messages.length - 1]
    if (lastMessage && lastMessage.type === 'assistant' && lastMessage.isStreaming) {
      console.log('🛑 停止请求时完成流式消息，内容长度:', lastMessage.content?.length || 0)
      chatState.messages.splice(chatState.messages.length - 1, 1, {
        ...lastMessage,
        isStreaming: false
      })
    }
  }

  /**
   * 处理来自扩展的消息
   */
  const handleExtensionMessage = (event: MessageEvent<ExtensionMessage>) => {
    const { type, data } = event.data
    console.log('🔄 前端收到消息:', type, data)
    console.log('🔄 完整消息对象:', event.data)
    console.log('📊 当前状态 - isProcessing:', chatState.isProcessing, 'isLoading:', chatState.isLoading, 'connectionStatus:', chatState.connectionStatus)

    switch (type) {
      case 'message':
        // 完整消息
        console.log('📝 前端收到完整消息:', data)
        
        addMessage({
          type: 'assistant',
          content: data.content || '',
          timestamp: Date.now()
        })
        chatState.isLoading = false
        chatState.connectionStatus = 'ready'
        scrollToBottom(true)
        break

      case 'error':
        // 错误处理
        console.log('前端收到错误:', data)
        
        addMessage({
          type: 'error',
          content: `❌ 错误: ${data.message || '未知错误'}`,
          timestamp: Date.now()
        })
        chatState.isLoading = false
        chatState.connectionStatus = 'error'
        chatState.isProcessing = false
        chatState.requestStartTime = undefined
        break

      case 'sessionInfo':
        // 会话信息
        console.log('前端收到会话信息:', data)
        chatState.sessionId = data.sessionId
        break

      case 'toolUse':
        // 工具调用
        console.log('前端收到工具调用:', data)
        addMessage({
          type: 'tool',
          content: '',
          timestamp: Date.now(),
          toolInfo: data.toolInfo,
          toolInput: data.toolInput,
          rawInput: data.rawInput,
          toolName: data.toolName,
          toolUseId: data.toolUseId
        })
        // 工具调用时不强制滚动，让用户决定
        break

      case 'toolResult':
        // 工具结果
        console.log('前端收到工具结果:', data)
        // 查找对应的工具调用消息并更新结果
        const toolMessage = chatState.messages.find(msg =>
          msg.type === 'tool' && msg.toolUseId === data.toolUseId
        )
        if (toolMessage) {
          // 更新工具消息的结果
          Object.assign(toolMessage, {
            content: data.content,
            isError: data.isError,
            hidden: data.hidden
          })
        } else {
          // 如果找不到对应的工具调用，创建新的结果消息
          addMessage({
            type: 'tool',
            content: data.content,
            timestamp: Date.now(),
            toolUseId: data.toolUseId,
            toolName: data.toolName,
            isError: data.isError,
            hidden: data.hidden
          })
        }
        // 工具结果更新时不强制滚动
        break

      case 'thinking':
        // 思考过程
        console.log('前端收到思考过程:', data)
        addMessage({
          type: 'thinking',
          content: data,
          timestamp: Date.now()
        })
        scrollToBottom()
        break

      case 'loading':
        // 加载消息
        console.log('前端收到加载消息:', data)
        addMessage({
          type: 'system',
          content: data,
          timestamp: Date.now()
        })
        break

      case 'setProcessing':
        // 设置处理状态
        console.log('🔧 前端设置处理状态:', data)
        console.log('🔧 设置前状态 - isProcessing:', chatState.isProcessing, 'isLoading:', chatState.isLoading, 'connectionStatus:', chatState.connectionStatus)

        chatState.isProcessing = data.isProcessing
        chatState.connectionStatus = data.isProcessing ? 'processing' : 'ready'

        if (data.isProcessing) {
          chatState.requestStartTime = data.requestStartTime
          console.log('🚀 开始处理，设置开始时间:', data.requestStartTime)
        } else {
          // 立即重置状态
          console.log('🛑 停止处理，立即重置状态...')
          chatState.requestStartTime = undefined
          chatState.isLoading = false
          console.log('✅ 状态重置完成')
        }

        console.log('🔧 设置后状态 - isProcessing:', chatState.isProcessing, 'isLoading:', chatState.isLoading, 'connectionStatus:', chatState.connectionStatus)
        console.log('🎯 StatusIndicator 应该收到的数据:', {
          connectionStatus: chatState.connectionStatus,
          isProcessing: chatState.isProcessing,
          totalCost: chatState.totalCost,
          requestStartTime: chatState.requestStartTime
        })
        break

      case 'clearLoading':
        // 清除加载消息
        console.log('前端清除加载消息')
        const messages = chatState.messages
        if (messages.length > 0) {
          const lastMessage = messages[messages.length - 1]
          if (lastMessage.type === 'system') {
            chatState.messages.pop()
          }
        }
        break

      case 'updateTokens':
      case 'updateTotals':
        // 更新统计信息
        console.log('前端更新统计信息:', data)
        if (data.totalCost !== undefined) chatState.totalCost = data.totalCost
        if (data.totalTokensInput !== undefined) chatState.totalTokensInput = data.totalTokensInput
        if (data.totalTokensOutput !== undefined) chatState.totalTokensOutput = data.totalTokensOutput
        if (data.requestCount !== undefined) chatState.requestCount = data.requestCount
        break



      case 'statusUpdate':
        // 状态更新
        console.log('前端状态更新:', data)
        if (data.connectionStatus) chatState.connectionStatus = data.connectionStatus
        if (data.totalCost !== undefined) chatState.totalCost = data.totalCost
        if (data.totalTokensInput !== undefined) chatState.totalTokensInput = data.totalTokensInput
        if (data.totalTokensOutput !== undefined) chatState.totalTokensOutput = data.totalTokensOutput
        if (data.requestCount !== undefined) chatState.requestCount = data.requestCount
        if (data.isProcessing !== undefined) {
          chatState.isProcessing = data.isProcessing
          chatState.requestStartTime = data.requestStartTime
        }
        break

      default:
        console.log('前端收到未知消息类型:', type, data)
    }
  }

  /**
   * 开始新会话
   */
  const startNewSession = () => {
    chatState.messages = []
    chatState.sessionId = undefined
    
    const message: WebviewMessage = {
      type: 'newSession'
    }
    vscodeAPI.postMessage(message)
  }

  /**
   * 清空聊天记录
   */
  const clearChat = () => {
    chatState.messages = []
    
    const message: WebviewMessage = {
      type: 'clearChat'
    }
    vscodeAPI.postMessage(message)
  }

  /**
   * 检查用户是否在消息底部
   */
  const isUserAtBottom = (): boolean => {
    const container = document.querySelector('.message-list')
    if (!container) return true
    
    const { scrollTop, scrollHeight, clientHeight } = container
    const threshold = 100 // 100px的缓冲区
    return scrollHeight - scrollTop - clientHeight < threshold
  }

  /**
   * 智能滚动到底部 - 只有用户在底部时才滚动
   */
  const scrollToBottom = (force = false) => {
    nextTick(() => {
      const container = document.querySelector('.message-list')
      if (container) {
        // 强制滚动或用户在底部时才执行滚动
        if (force || isUserAtBottom()) {
          container.scrollTop = container.scrollHeight
        }
      }
    })
  }

  /**
   * 初始化消息监听
   */
  const initMessageListener = () => {
    console.log('🔧 初始化消息监听器')
    window.addEventListener('message', handleExtensionMessage)
    console.log('✅ 消息监听器已设置')
  }

  /**
   * 清理资源
   */
  const cleanup = () => {
    window.removeEventListener('message', handleExtensionMessage)
  }

  return {
    // 状态
    chatState,
    currentInput,

    // 方法
    sendMessage,
    stopRequest,
    startNewSession,
    clearChat,
    initMessageListener,
    cleanup,
    scrollToBottom
  }
}
