import * as vscode from 'vscode'
import * as cp from 'child_process'
import { EventEmitter } from 'events'

/**
 * <PERSON>响应数据接口
 */
export interface ClaudeResponse {
  type: 'content' | 'session' | 'error' | 'complete' | 'toolUse' | 'toolResult' | 'usage'
  content?: string
  sessionId?: string
  error?: string
  metadata?: any
  // 工具调用相关
  toolInfo?: string
  toolInput?: any
  rawInput?: any
  toolName?: string
  toolUseId?: string
  isError?: boolean
  hidden?: boolean
  // 使用统计相关
  usage?: {
    totalCost?: number
    totalTokensInput?: number
    totalTokensOutput?: number
    requestCount?: number
  }
}

/**
 * Claude配置接口
 */
export interface ClaudeConfig {
  wslEnabled: boolean
  wslDistro: string
  nodePath: string
  claudePath: string
  model?: string
}

/**
 * Claude服务类 - 负责与Claude CLI交互
 */
export class ClaudeService extends EventEmitter {
  private _currentProcess: cp.ChildProcess | undefined
  private _currentSessionId: string | undefined
  private _isProcessing: boolean = false
  private _config: ClaudeConfig
  private _toolUseCache: Map<string, string> = new Map() // 缓存工具ID到名称的映射
  
  // 统计数据
  private _totalCost: number = 0
  private _totalTokensInput: number = 0
  private _totalTokensOutput: number = 0
  private _requestCount: number = 0


  constructor() {
    super()
    this._config = this._loadConfig()
  }

  /**
   * 加载配置
   */
  private _loadConfig(): ClaudeConfig {
    const config = vscode.workspace.getConfiguration('claudeChat')
    return {
      wslEnabled: config.get<boolean>('wsl.enabled', false),
      wslDistro: config.get<string>('wsl.distro', 'Ubuntu'),
      nodePath: config.get<string>('wsl.nodePath', '/usr/bin/node'),
      claudePath: config.get<string>('wsl.claudePath', '/usr/local/bin/claude'),
      model: config.get<string>('model', 'default')
    }
  }

  /**
   * 发送消息给Claude
   */
  public async sendMessage(message: string): Promise<void> {
    if (this._isProcessing) {
      throw new Error('Claude正在处理中，请稍候...')
    }

    this._isProcessing = true
    this._requestCount++

    // 📊 记录请求开始时的统计数据快照
    console.log('🚀 [REQUEST-START] 请求开始前的统计数据快照:', {
      totalCost: this._totalCost,
      totalTokensInput: this._totalTokensInput,
      totalTokensOutput: this._totalTokensOutput,
      requestCount: this._requestCount
    })

    this.emit('processingStart')

    // 发送初始统计数据
    this._emitUsageData()

    try {
      await this._executeClaudeCommand(message)
    } catch (error) {
      this.emit('error', error)
      throw error
    } finally {
      this._isProcessing = false
      this.emit('processingEnd')

      // 📊 记录请求结束时的统计数据快照
      console.log('🏁 [REQUEST-END] 请求结束后的统计数据快照:', {
        totalCost: this._totalCost,
        totalTokensInput: this._totalTokensInput,
        totalTokensOutput: this._totalTokensOutput,
        requestCount: this._requestCount
      })

      console.log('📊 等待Claude CLI返回真实usage数据...')
    }
  }

  /**
   * 执行Claude命令
   */
  private async _executeClaudeCommand(message: string): Promise<void> {
    const args = this._buildCommandArgs()
    const claudeProcess = this._spawnClaudeProcess(args)
    
    this._currentProcess = claudeProcess

    // 发送消息到Claude
    if (claudeProcess.stdin) {
      console.log('发送消息到Claude:', message)
      claudeProcess.stdin.write(message + '\n')
      claudeProcess.stdin.end()
      console.log('消息发送完成，stdin已关闭')
    } else {
      console.error('Claude进程stdin不可用')
    }

    // 处理输出流
    this._handleProcessStreams(claudeProcess)

    // 监控进程状态
    console.log('Claude进程PID:', claudeProcess.pid)
    console.log('Claude进程已启动，等待响应...')
  }

  /**
   * 构建命令参数
   */
  private _buildCommandArgs(): string[] {
    const args = [
      '-p',
      '--output-format', 'stream-json',
      '--verbose'
    ]

    // 添加模型选择
    if (this._config.model && this._config.model !== 'default') {
      args.push('--model', this._config.model)
    }

    // 添加会话恢复
    if (this._currentSessionId) {
      args.push('--resume', this._currentSessionId)
    }

    return args
  }

  /**
   * 启动Claude进程
   */
  private _spawnClaudeProcess(args: string[]): cp.ChildProcess {
    const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd()

    if (this._config.wslEnabled) {
      // WSL模式
      const wslCommand = `"${this._config.nodePath}" --no-warnings --enable-source-maps "${this._config.claudePath}" ${args.join(' ')}`
      console.log('启动WSL Claude命令:', wslCommand)

      return cp.spawn('wsl', ['-d', this._config.wslDistro, 'bash', '-ic', wslCommand], {
        cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          FORCE_COLOR: '0',
          NO_COLOR: '1'
        }
      })
    } else {
      // 原生模式
      console.log('启动原生Claude命令:', 'claude', args.join(' '))
      return cp.spawn('claude', args, {
        shell: process.platform === 'win32',
        cwd,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          FORCE_COLOR: '0',
          NO_COLOR: '1'
        }
      })
    }
  }

  /**
   * 处理进程流
   */
  private _handleProcessStreams(claudeProcess: cp.ChildProcess): void {
    let rawOutput = ''
    let errorOutput = ''

    // 处理标准输出
    if (claudeProcess.stdout) {
      claudeProcess.stdout.on('data', (data) => {
        rawOutput += data.toString()

        // 按行处理JSON流，保留未完成的行
        const lines = rawOutput.split('\n')
        rawOutput = lines.pop() || '' // 保留最后一行（可能不完整）

        for (const line of lines) {
          if (line.trim()) {
            try {
              const jsonData = JSON.parse(line.trim())
              this._handleJsonData(jsonData)
            } catch (error) {
              console.log('解析JSON失败:', line, error)
            }
          }
        }

        // 检查是否还有未处理的数据
        if (rawOutput.trim()) {
          console.log('缓冲区剩余数据:', rawOutput)
        }
      })
    }

    // 处理错误输出
    if (claudeProcess.stderr) {
      claudeProcess.stderr.on('data', (data) => {
        errorOutput += data.toString()
        console.log('Claude stderr:', data.toString())
      })
    }

    // 处理进程结束
    claudeProcess.on('close', (code) => {
      console.log('🔚 Claude进程结束，退出代码:', code)
      console.log('🔚 错误输出:', errorOutput)

      this._currentProcess = undefined

      if (code !== 0) {
        if (errorOutput.trim()) {
          console.log('❌ 进程错误，触发error事件')
          this.emit('error', new Error(`Claude进程错误: ${errorOutput.trim()}`))
        } else {
          console.log('❌ 进程异常退出，触发error事件')
          this.emit('error', new Error(`Claude进程退出，代码: ${code}`))
        }
      } else {
        console.log('✅ 进程正常结束，触发complete事件')
        this.emit('complete')
      }
    })

    // 处理进程错误
    claudeProcess.on('error', (error) => {
      this._currentProcess = undefined
      
      if (error.message.includes('ENOENT') || error.message.includes('command not found')) {
        this.emit('error', new Error('Claude CLI未安装，请访问: https://www.anthropic.com/claude-code'))
      } else {
        this.emit('error', new Error(`Claude进程错误: ${error.message}`))
      }
    })
  }



  /**
   * 处理JSON数据 - 参考老项目实现
   */
  private _handleJsonData(jsonData: any): void {
    console.log('🔄 [JSON-DATA] 收到JSON数据 - 类型:', jsonData.type)
    console.log('🔄 [JSON-DATA] 完整数据:', JSON.stringify(jsonData, null, 2))

    switch (jsonData.type) {
      case 'system':
        if (jsonData.subtype === 'init') {
          // 系统初始化 - 获取会话ID
          console.log('系统初始化，会话ID:', jsonData.session_id)
          this._currentSessionId = jsonData.session_id
          this.emit('response', {
            type: 'session',
            sessionId: jsonData.session_id
          } as ClaudeResponse)
        }
        break

      case 'assistant':
        if (jsonData.message && jsonData.message.content) {
          console.log('🤖 [ASSISTANT] 处理assistant消息:', jsonData.message.content)

          // 🔍 检查assistant消息中是否有usage数据（仅记录，不显示，避免重复）
          if (jsonData.message.usage) {
            console.log('📊 [ASSISTANT-USAGE] 发现assistant消息中的usage数据（仅记录）:', jsonData.message.usage)
            console.log('⚠️ [ASSISTANT-USAGE] 跳过显示，等待result消息中的最终统计')
          } else {
            console.log('📊 [ASSISTANT-USAGE] assistant消息中无usage数据')
          }

          // 处理助手消息内容
          for (const content of jsonData.message.content) {
            if (content.type === 'text') {
              console.log('发送文本内容:', content.text)
              // 直接发送完整内容，不模拟流式
              this.emit('response', {
                type: 'content',
                content: content.text
              } as ClaudeResponse)
            } else if (content.type === 'tool_use') {
              // 处理工具调用
              console.log('发送工具调用:', content)
              const toolInfo = `🔧 Executing: ${content.name}`
              let toolInput = ''

              if (content.input) {
                // 特殊格式化处理
                if (content.name === 'TodoWrite' && content.input.todos) {
                  toolInput = '\nTodo List Update:'
                  for (const todo of content.input.todos) {
                    const status = todo.status === 'completed' ? '✅' :
                      todo.status === 'in_progress' ? '🔄' : '⏳'
                    toolInput += `\n${status} ${todo.content} (priority: ${todo.priority})`
                  }
                } else {
                  // 发送原始输入到UI进行格式化
                  toolInput = ''
                }
              }

              // 缓存工具ID到名称的映射
              this._toolUseCache.set(content.id, content.name)

              // 发送工具调用事件
              this.emit('response', {
                type: 'toolUse',
                toolInfo: toolInfo,
                toolInput: toolInput,
                rawInput: content.input,
                toolName: content.name,
                toolUseId: content.id
              } as ClaudeResponse)
            }
          }
        }
        break

      case 'result':
        // 处理结果消息，通常表示完成
        if (jsonData.subtype === 'success') {
          console.log('🎯 [RESULT] Claude处理完成，触发complete事件')
          console.log('🎯 [RESULT] 完整结果数据:', JSON.stringify(jsonData, null, 2))

          // 🔍 检查result消息中的usage数据
          if (jsonData.usage) {
            console.log('📊 [RESULT-USAGE] 发现result消息中的usage数据:', jsonData.usage)
            this._parseUsageData(jsonData.usage, 'RESULT-USAGE')
          } else {
            console.log('📊 [RESULT-USAGE] result消息中无usage数据')
          }

          // 🔍 检查result消息中的其他可能的费用字段
          if (jsonData.total_cost_usd) {
            console.log('💰 [RESULT-COST] 发现total_cost_usd字段:', jsonData.total_cost_usd)
            this._parseUsageData({ cost: jsonData.total_cost_usd }, 'RESULT-TOTAL_COST_USD')
          }

          if (jsonData.cost) {
            console.log('💰 [RESULT-COST] 发现cost字段:', jsonData.cost)
            this._parseUsageData({ cost: jsonData.cost }, 'RESULT-COST')
          }

          this.emit('complete')
        }
        break

      case 'usage':
        // 处理使用统计数据
        console.log('📊 [USAGE] 收到专门的usage消息:', JSON.stringify(jsonData, null, 2))
        this._parseUsageData(jsonData, 'USAGE-MESSAGE')
        break

      case 'user':
        if (jsonData.message && jsonData.message.content) {
          // 处理工具结果
          for (const content of jsonData.message.content) {
            if (content.type === 'tool_result') {
              let resultContent = content.content || 'Tool executed successfully'

              // 如果内容是对象或数组，转换为字符串
              if (typeof resultContent === 'object' && resultContent !== null) {
                resultContent = JSON.stringify(resultContent, null, 2)
              }

              const isError = content.is_error || false
              const toolName = this._getToolNameFromId(content.tool_use_id)

              console.log('发送工具结果:', {
                toolName,
                isError,
                content: resultContent
              })

              // 某些工具的结果不显示（除非有错误）
              const hiddenTools = ['Read', 'Edit', 'TodoWrite', 'MultiEdit']
              const shouldHide = hiddenTools.includes(toolName) && !isError

              // 发送工具结果事件
              this.emit('response', {
                type: 'toolResult',
                content: resultContent,
                isError: isError,
                toolUseId: content.tool_use_id,
                toolName: toolName,
                hidden: shouldHide
              } as ClaudeResponse)
            }
          }
        }
        break

      default:
        console.log('❓ [UNKNOWN] 未知消息类型:', jsonData.type, '完整数据:', JSON.stringify(jsonData, null, 2))

        // 🔍 检查未知消息类型中是否有usage相关字段
        if (jsonData.usage) {
          console.log('📊 [UNKNOWN-USAGE] 在未知消息类型中发现usage数据:', jsonData.usage)
          this._parseUsageData(jsonData.usage, 'UNKNOWN-USAGE')
        }

        if (jsonData.total_cost_usd || jsonData.cost) {
          console.log('💰 [UNKNOWN-COST] 在未知消息类型中发现费用数据:', {
            total_cost_usd: jsonData.total_cost_usd,
            cost: jsonData.cost
          })
        }
    }
  }

  /**
   * 根据工具使用ID获取工具名称
   */
  private _getToolNameFromId(toolUseId: string): string {
    return this._toolUseCache.get(toolUseId) || 'Unknown Tool'
  }

  /**
   * 开始新会话
   */
  public newSession(): void {
    this._currentSessionId = undefined
    this.stopCurrentProcess()
  }

  /**
   * 停止当前进程
   */
  public stopCurrentProcess(): void {
    if (this._currentProcess) {
      this._currentProcess.kill('SIGTERM')
      this._currentProcess = undefined
    }
    this._isProcessing = false
  }

  /**
   * 获取当前会话ID
   */
  public getCurrentSessionId(): string | undefined {
    return this._currentSessionId
  }

  /**
   * 检查是否正在处理
   */
  public isProcessing(): boolean {
    return this._isProcessing
  }

  /**
   * 重新加载配置
   */
  public reloadConfig(): void {
    this._config = this._loadConfig()
  }



  /**
   * 解析使用统计数据
   */
  private _parseUsageData(usageData: any, source?: string): void {
    const caller = source || new Error().stack?.split('\n')[2]?.trim() || 'unknown'
    console.log('📊 [PARSE-USAGE] 解析使用统计数据 - 调用来源:', caller)
    console.log('📊 [PARSE-USAGE] 原始usage数据:', JSON.stringify(usageData, null, 2))

    let hasRealData = false

    // 解析费用信息
    if (usageData.cost !== undefined) {
      this._totalCost += parseFloat(usageData.cost) || 0
      hasRealData = true
      console.log('💰 [真实数据] 费用增加:', usageData.cost)
    }

    // 解析token信息
    if (usageData.input_tokens !== undefined) {
      this._totalTokensInput += parseInt(usageData.input_tokens) || 0
      hasRealData = true
      console.log('📥 [真实数据] 输入tokens增加:', usageData.input_tokens)
    }

    if (usageData.output_tokens !== undefined) {
      this._totalTokensOutput += parseInt(usageData.output_tokens) || 0
      hasRealData = true
      console.log('📤 [真实数据] 输出tokens增加:', usageData.output_tokens)
    }

    // 解析总计信息（如果提供的是累计值）
    if (usageData.total_cost !== undefined) {
      this._totalCost = parseFloat(usageData.total_cost) || 0
      hasRealData = true
      console.log('💰 [真实数据] 总费用设置为:', usageData.total_cost)
    }

    if (usageData.total_input_tokens !== undefined) {
      this._totalTokensInput = parseInt(usageData.total_input_tokens) || 0
      hasRealData = true
      console.log('📥 [真实数据] 总输入tokens设置为:', usageData.total_input_tokens)
    }

    if (usageData.total_output_tokens !== undefined) {
      this._totalTokensOutput = parseInt(usageData.total_output_tokens) || 0
      hasRealData = true
      console.log('📤 [真实数据] 总输出tokens设置为:', usageData.total_output_tokens)
    }

    if (hasRealData) {
      console.log('✅ [真实数据] 更新后的统计数据:', {
        totalCost: this._totalCost,
        totalTokensInput: this._totalTokensInput,
        totalTokensOutput: this._totalTokensOutput,
        requestCount: this._requestCount
      })
    } else {
      console.log('⚠️ [真实数据] 未找到有效的usage数据字段')
    }

    // 发送更新后的统计数据
    this._emitUsageData()
  }

  /**
   * 发送使用统计数据
   */
  private _emitUsageData(): void {
    this.emit('response', {
      type: 'usage',
      usage: {
        totalCost: this._totalCost,
        totalTokensInput: this._totalTokensInput,
        totalTokensOutput: this._totalTokensOutput,
        requestCount: this._requestCount
      }
    } as ClaudeResponse)
  }



  /**
   * 获取当前统计数据
   */
  public getUsageStats() {
    return {
      totalCost: this._totalCost,
      totalTokensInput: this._totalTokensInput,
      totalTokensOutput: this._totalTokensOutput,
      requestCount: this._requestCount
    }
  }

  /**
   * 重置统计数据
   */
  public resetUsageStats(): void {
    this._totalCost = 0
    this._totalTokensInput = 0
    this._totalTokensOutput = 0
    this._requestCount = 0
    this._emitUsageData()
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.stopCurrentProcess()
    this.removeAllListeners()
  }
}
