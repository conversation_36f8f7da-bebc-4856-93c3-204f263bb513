/**
 * VSCode API 管理工具
 * 统一管理webview与扩展的通信
 */

// 声明window类型扩展
declare global {
  interface Window {
    acquireVsCodeApi(): any
    vscode?: any
  }
}

/**
 * VSCode API 单例管理类
 */
class VSCodeAPI {
  private static instance: VSCodeAPI
  private vscode: any = null
  private initialized = false

  private constructor() {
    this.initialize()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): VSCodeAPI {
    if (!VSCodeAPI.instance) {
      VSCodeAPI.instance = new VSCodeAPI()
    }
    return VSCodeAPI.instance
  }

  /**
   * 初始化VSCode API
   */
  private initialize(): void {
    try {
      if (typeof window !== 'undefined' && window.acquireVsCodeApi) {
        // 检查是否已经获取过API
        if (window.vscode) {
          this.vscode = window.vscode
          console.log('📡 使用已存在的VSCode API实例')
        } else {
          // 获取新的API实例
          this.vscode = window.acquireVsCodeApi()
          // 保存到全局，避免重复获取
          window.vscode = this.vscode
          console.log('📡 成功获取VSCode API实例')
        }
        this.initialized = true
      } else {
        console.warn('⚠️ VSCode API不可用，可能不在webview环境中')
      }
    } catch (error) {
      console.error('❌ 初始化VSCode API失败:', error)
    }
  }

  /**
   * 检查API是否可用
   */
  public isAvailable(): boolean {
    return this.initialized && this.vscode !== null
  }

  /**
   * 发送消息到扩展
   */
  public postMessage(message: any): boolean {
    if (!this.isAvailable()) {
      console.error('❌ VSCode API不可用，无法发送消息')
      return false
    }

    try {
      this.vscode.postMessage(message)
      console.log('📤 已发送消息到扩展:', message)
      return true
    } catch (error) {
      console.error('❌ 发送消息失败:', error)
      return false
    }
  }

  /**
   * 打开文件
   */
  public openFile(filePath: string): boolean {
    console.log('📂 请求打开文件:', filePath)
    
    return this.postMessage({
      type: 'openFile',
      filePath: filePath
    })
  }

  /**
   * 发送用户消息
   */
  public sendMessage(content: string): boolean {
    return this.postMessage({
      type: 'sendMessage',
      content: content
    })
  }

  /**
   * 停止处理
   */
  public stopProcessing(): boolean {
    return this.postMessage({
      type: 'stopProcessing'
    })
  }

  /**
   * 清空聊天记录
   */
  public clearChat(): boolean {
    return this.postMessage({
      type: 'clearChat'
    })
  }

  /**
   * 重新加载webview（开发模式）
   */
  public requestReload(): boolean {
    return this.postMessage({
      type: 'requestReload'
    })
  }
}

// 导出单例实例
export const vscodeAPI = VSCodeAPI.getInstance()

// 导出便捷方法
export const openFile = (filePath: string) => vscodeAPI.openFile(filePath)
export const sendMessage = (content: string) => vscodeAPI.sendMessage(content)
export const stopProcessing = () => vscodeAPI.stopProcessing()
export const clearChat = () => vscodeAPI.clearChat()
export const requestReload = () => vscodeAPI.requestReload()

// 默认导出
export default vscodeAPI
