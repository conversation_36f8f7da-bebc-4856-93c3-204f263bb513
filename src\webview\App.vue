<template>
  <div id="app" class="app-container">
    <ChatContainer />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import ChatContainer from './components/ChatContainer.vue'

/**
 * 应用初始化
 */
onMounted(() => {
  // 通知VSCode扩展webview已准备就绪
  const vscode = window.acquireVsCodeApi()
  vscode.postMessage({
    type: 'webviewReady'
  })
  
  // 设置VSCode主题类
  updateThemeClass()
  
  // 监听主题变化
  const observer = new MutationObserver(() => {
    updateThemeClass()
  })
  
  observer.observe(document.body, {
    attributes: true,
    attributeFilter: ['class', 'data-vscode-theme-kind']
  })
})

/**
 * 更新主题类名
 */
const updateThemeClass = () => {
  const body = document.body
  const themeKind = body.getAttribute('data-vscode-theme-kind')
  
  // 移除现有主题类
  body.classList.remove('vscode-light', 'vscode-dark', 'vscode-high-contrast')
  
  // 添加对应主题类
  switch (themeKind) {
    case 'vscode-light':
      body.classList.add('vscode-light')
      document.documentElement.classList.remove('dark')
      break
    case 'vscode-dark':
      body.classList.add('vscode-dark')
      document.documentElement.classList.add('dark')
      break
    case 'vscode-high-contrast':
      body.classList.add('vscode-high-contrast')
      document.documentElement.classList.add('dark')
      break
    default:
      // 默认使用深色主题
      body.classList.add('vscode-dark')
      document.documentElement.classList.add('dark')
  }
}
</script>

<style>
/* 全局样式 */
.app-container {
  @apply w-full h-screen overflow-hidden;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  color: var(--vscode-foreground);
  background-color: var(--vscode-editor-background);
}

/* VSCode主题适配 */
.vscode-light {
  color-scheme: light;
}

.vscode-dark {
  color-scheme: dark;
}

.vscode-high-contrast {
  color-scheme: dark;
}

/* 确保全屏显示 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

/* 禁用文本选择（可选） */
.app-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许消息内容选择 */
.message-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 允许输入框选择 */
.input-field {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
</style>
