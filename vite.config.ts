import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === 'development'
  
  return {
    plugins: [vue()],
    build: {
      outDir: 'out/webview',
      lib: {
        entry: resolve(__dirname, 'src/webview/main.ts'),
        name: 'ClaudeCodeWebview',
        fileName: 'webview',
        formats: ['iife']
      },
      rollupOptions: {
        external: [],
        output: {
          globals: {}
        }
      },
      minify: isDev ? false : 'esbuild',
      sourcemap: true,
      // 开发模式下加快构建速度
      target: isDev ? 'es2020' : 'es2015',
      reportCompressedSize: !isDev
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode || 'development')
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/webview')
      }
    },
    server: {
      port: 3000,
      hmr: {
        port: 3001
      }
    },
    // 开发模式优化
    optimizeDeps: {
      include: ['vue']
    }
  }
})
