<template>
  <!-- 工具调用消息 -->
  <ToolUseMessage
    v-if="message.type === 'tool'"
    :tool-info="message.toolInfo || ''"
    :tool-input="message.toolInput"
    :raw-input="message.rawInput"
    :tool-name="message.toolName || ''"
    :tool-use-id="message.toolUseId"
    :timestamp="message.timestamp"
    :tool-result="toolResult"
    :is-loading="isToolLoading"
  />

  <!-- 思考过程消息 -->
  <ThinkingMessage
    v-else-if="message.type === 'thinking'"
    :content="message.content"
    :timestamp="message.timestamp"
    :is-active="message.isStreaming"
  />

  <!-- 系统消息 -->
  <div v-else-if="message.type === 'system'" class="system-message">
    <div class="system-content">
      <span class="system-icon">ℹ️</span>
      <span class="system-text">{{ message.content }}</span>
    </div>
  </div>

  <!-- 错误消息 -->
  <div v-else-if="message.type === 'error'" class="error-message">
    <div class="error-content">
      <span class="error-icon">⚠️</span>
      <span class="error-text">{{ message.content }}</span>
    </div>
  </div>

  <!-- 普通用户/助手消息 -->
  <div
    v-else
    :class="[
      'message-item flex',
      message.type === 'user' ? 'justify-end' : 'justify-start',
    ]"
  >
    <div
      :class="[
        'message-content',
        message.type === 'user' ? 'message-user' : 'message-assistant w-full',
      ]"
    >
      <!-- 消息头部 -->
      <div
        :class="[
          'flex items-center mb-1',
          message.type === 'user' ? 'justify-end' : 'justify-start',
        ]"
      >
        <div
          v-if="message.type === 'assistant'"
          class="flex items-center space-x-1.5"
        >
          <div
            class="w-4 h-4 rounded-full bg-green-500 text-white flex items-center justify-center text-[10px] font-bold"
          >
            C
          </div>
          <span class="vscode-font-size-sm font-medium">Claude</span>
          <span class="vscode-font-size-xs opacity-50">{{
            formatTime(message.timestamp)
          }}</span>
        </div>

        <div v-else class="flex items-center space-x-1.5">
          <span class="vscode-font-size-xs opacity-50">{{
            formatTime(message.timestamp)
          }}</span>
          <span class="vscode-font-size-sm font-medium">您</span>
          <div
            class="w-4 h-4 rounded-full bg-blue-500 text-white flex items-center justify-center text-[10px] font-bold"
          >
            U
          </div>
        </div>
      </div>

      <!-- 消息内容 -->
      <div class="message-text">
        <div
          v-if="message.content"
          class="markdown-content"
          v-html="formattedContent"
        />

        <!-- 流式输入指示器 -->
        <div
          v-if="message.isStreaming"
          class="flex items-center space-x-1 mt-1"
        >
          <div class="flex space-x-1">
            <div
              class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"
            ></div>
            <div
              class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"
              style="animation-delay: 0.2s"
            ></div>
            <div
              class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"
              style="animation-delay: 0.4s"
            ></div>
          </div>
          <span class="vscode-font-size-xs opacity-50">正在输入...</span>
        </div>

        <!-- 流式光标效果 -->
        <span
          v-if="message.isStreaming && message.content"
          class="inline-block w-0.5 h-4 bg-green-500 animate-pulse ml-0.5"
        ></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import type { Message, ToolResultData } from "../../types";
import { parseMarkdown, setupCodeCopyFunction } from "../utils/markdown";
import ToolUseMessage from "./ToolUseMessage.vue";
import ThinkingMessage from "./ThinkingMessage.vue";

// Props
interface Props {
  message: Message;
}

const props = defineProps<Props>();

/**
 * 工具结果数据
 */
const toolResult = computed((): ToolResultData | undefined => {
  if (props.message.type !== "tool" || !props.message.content) return undefined;

  return {
    content: props.message.content,
    isError: props.message.isError || false,
    toolUseId: props.message.toolUseId || "",
    toolName: props.message.toolName || "",
    hidden: props.message.hidden,
  };
});

/**
 * 工具是否正在加载
 */
const isToolLoading = computed(() => {
  return (
    props.message.type === "tool" &&
    props.message.toolUseId &&
    !props.message.content &&
    !props.message.isError
  );
});

/**
 * 格式化时间显示
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

/**
 * 格式化消息内容
 * 使用markdown-it进行完整的Markdown渲染
 */
const formattedContent = computed(() => {
  if (!props.message.content) {
    return "";
  }

  return parseMarkdown(props.message.content);
});

/**
 * 组件挂载时设置复制功能
 */
onMounted(() => {
  setupCodeCopyFunction();
});
</script>

<style scoped>
.message-content {
  @apply transition-all duration-200 backdrop-blur-sm;
  background: rgba(var(--vscode-editor-background-rgb, 30, 30, 30), 0.1);
}

.message-user {
  border: 2px solid rgba(59, 130, 246, 0.8) !important;
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.18) !important;
  backdrop-filter: blur(8px);
}

.message-assistant {
  border: 2px solid rgba(34, 197, 94, 0.8) !important;
  box-shadow: 0 2px 12px rgba(34, 197, 94, 0.18) !important;
  backdrop-filter: blur(8px);
}

.message-text {
  @apply leading-relaxed;
}

/* 强制覆盖Markdown样式 */
.message-content :deep(.markdown-content h1) {
  font-size: calc(var(--vscode-font-size, 13px) * 1.20) !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.25 !important;
  border-bottom: 1px solid var(--vscode-panel-border) !important;
  padding-bottom: 0.25rem !important;
}

.message-content :deep(.markdown-content h2) {
  font-size: calc(var(--vscode-font-size, 13px) * 1.10) !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.25 !important;
  border-bottom: 1px solid var(--vscode-panel-border) !important;
  padding-bottom: 0.125rem !important;
}

.message-content :deep(.markdown-content h3) {
  font-size: calc(var(--vscode-font-size, 13px) * 1.05) !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 0.25rem 0 !important;
  line-height: 1.25 !important;
}

.message-content :deep(.markdown-content p) {
  font-size: var(--vscode-font-size, 13px) !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.625 !important;
}

.message-content :deep(.markdown-content ul),
.message-content :deep(.markdown-content ol) {
  font-size: var(--vscode-font-size, 13px) !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1rem !important;
}

/* 系统消息样式 */
.system-message {
  margin: 8px 0;
  padding: 8px 12px;
  background: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  border-left: 3px solid var(--vscode-progressBar-background);
}

.system-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-foreground);
}

.system-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.system-text {
  opacity: 0.9;
}

/* 错误消息样式 */
.error-message {
  margin: 8px 0;
  padding: 8px 12px;
  background: rgba(255, 69, 58, 0.1);
  border: 1px solid var(--vscode-errorForeground);
  border-radius: 6px;
  border-left: 3px solid var(--vscode-errorForeground);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-errorForeground);
}

.error-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.error-text {
  opacity: 0.9;
}
</style>
