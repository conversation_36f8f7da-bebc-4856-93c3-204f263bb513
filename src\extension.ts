import * as vscode from 'vscode'
import * as cp from 'child_process'
import * as path from 'path'
import { ClaudeService, ClaudeResponse } from './services/ClaudeService'

/**
 * 扩展激活函数
 */
export function activate(context: vscode.ExtensionContext) {
  console.log('🚀 Claude Chat 扩展正在激活...')

  const provider = new ClaudeCodeProvider(context.extensionUri, context)

  // 注册命令
  const disposable = vscode.commands.registerCommand(
    'claude-code.openChat',
    (column?: vscode.ViewColumn) => {
      console.log('📱 Claude Chat 聊天命令执行')
      provider.show(column)
    }
  )

  // 注册侧边栏webview提供者
  const webviewProvider = new ClaudeCodeWebviewProvider(
    context.extensionUri,
    context,
    provider
  )
  vscode.window.registerWebviewViewProvider('claude-code.chat', webviewProvider, {
    webviewOptions: {
      retainContextWhenHidden: true
    }
  })

  // 创建状态栏项
  const statusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Right,
    100
  )
  statusBarItem.text = "🤖 Claude"
  statusBarItem.tooltip = "打开 Claude Chat (Ctrl+Shift+C)"
  statusBarItem.command = 'claude-code.openChat'
  statusBarItem.show()

  context.subscriptions.push(disposable, statusBarItem)
  console.log('✅ Claude Chat 扩展激活完成')
}

/**
 * 扩展停用函数
 */
export function deactivate() {
  console.log('👋 Claude Code 扩展已停用')
}

/**
 * 侧边栏Webview提供者
 */
class ClaudeCodeWebviewProvider implements vscode.WebviewViewProvider {
  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly _context: vscode.ExtensionContext,
    private readonly _chatProvider: ClaudeCodeProvider
  ) {}

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri]
    }

    // 使用共享的聊天提供者实例
    this._chatProvider.showInWebview(webviewView.webview, webviewView)

    // 处理可见性变化
    webviewView.onDidChangeVisibility(() => {
      if (webviewView.visible) {
        // 关闭主面板（如果打开）
        if (this._chatProvider._panel) {
          console.log('🔄 关闭主面板，因为侧边栏变为可见')
          this._chatProvider._panel.dispose()
          this._chatProvider._panel = undefined
        }
        this._chatProvider.reinitializeWebview()
      }
    })
  }
}

/**
 * Claude Chat 主提供者类
 */
class ClaudeCodeProvider {
  public _panel: vscode.WebviewPanel | undefined
  private _webview: vscode.Webview | undefined
  private _webviewView: vscode.WebviewView | undefined
  private _disposables: vscode.Disposable[] = []
  private _messageHandlerDisposable: vscode.Disposable | undefined
  private _claudeService: ClaudeService
  private _currentStreamingMessage: string = ''
  private _hasProcessingStarted: boolean = false
  private _isWebviewReady: boolean = false

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly _context: vscode.ExtensionContext
  ) {
    this._claudeService = new ClaudeService()
    this._setupClaudeServiceListeners()
  }

  /**
   * 设置Claude服务监听器
   */
  private _setupClaudeServiceListeners() {
    this._claudeService.on('processingStart', () => {
      this._currentStreamingMessage = ''
      this._hasProcessingStarted = true
      console.log('开始处理Claude请求')

      // 发送处理状态
      this._postMessage({
        type: 'setProcessing',
        data: {
          isProcessing: true,
          requestStartTime: Date.now()
        }
      })

      // 开始处理，等待完整消息
      console.log('🚀 开始处理Claude请求，等待完整响应')
    })

    this._claudeService.on('response', (response: ClaudeResponse) => {
      switch (response.type) {
        case 'content':
          // 累积内容，不实时发送
          this._currentStreamingMessage += response.content || ''
          console.log('📝 累积内容，当前长度:', this._currentStreamingMessage.length)
          break

        case 'session':
          this._postMessage({
            type: 'sessionInfo',
            data: { sessionId: response.sessionId }
          })
          break

        case 'toolUse':
          // 工具调用
          console.log('🔧 发送工具调用到前端:', response)
          this._postMessage({
            type: 'toolUse',
            data: {
              toolInfo: response.toolInfo,
              toolInput: response.toolInput,
              rawInput: response.rawInput,
              toolName: response.toolName,
              toolUseId: response.toolUseId
            }
          })
          break

        case 'toolResult':
          // 工具结果
          console.log('📋 发送工具结果到前端:', response)
          this._postMessage({
            type: 'toolResult',
            data: {
              content: response.content,
              isError: response.isError,
              toolUseId: response.toolUseId,
              toolName: response.toolName,
              hidden: response.hidden
            }
          })
          break

        case 'usage':
          // 使用统计数据
          console.log('📊 发送使用统计到前端:', response.usage)
          this._postMessage({
            type: 'updateTotals',
            data: response.usage
          })
          break



        case 'error':
          this._postMessage({
            type: 'error',
            data: { message: response.error || '未知错误' }
          })
          break
      }
    })

    this._claudeService.on('complete', () => {
      // 一次性发送完整消息
      if (this._hasProcessingStarted) {
        console.log('🏁 Claude输出完成，发送完整消息')
        console.log('🏁 完整消息长度:', this._currentStreamingMessage.length)

        // 发送完整消息
        this._postMessage({
          type: 'message',
          data: { content: this._currentStreamingMessage }
        })

        // 发送处理完成状态
        console.log('🏁 发送处理完成状态: isProcessing = false')
        this._postMessage({
          type: 'setProcessing',
          data: {
            isProcessing: false
          }
        })

        this._hasProcessingStarted = false
        this._currentStreamingMessage = ''
        console.log('🏁 完成状态重置')
      }
    })

    this._claudeService.on('error', (error: Error) => {
      this._postMessage({
        type: 'error',
        data: { message: error.message }
      })
    })
  }

  /**
   * 显示主面板
   */
  public show(column: vscode.ViewColumn | vscode.Uri = vscode.ViewColumn.Two) {
    const actualColumn = column instanceof vscode.Uri ? vscode.ViewColumn.Two : column

    // 关闭侧边栏
    this._closeSidebar()

    if (this._panel) {
      this._panel.reveal(actualColumn)
      return
    }

    this._panel = vscode.window.createWebviewPanel(
      'claudeCode',
      'Claude Chat',
      actualColumn,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [this._extensionUri]
      }
    )

    // 设置图标
    const iconPath = vscode.Uri.joinPath(this._extensionUri, 'claude-code-chat-老版本', 'icon.png')
    this._panel.iconPath = iconPath

    this._panel.webview.html = this._getHtmlForWebview()
    this._panel.onDidDispose(() => this.dispose(), null, this._disposables)
    this._setupWebviewMessageHandler(this._panel.webview)
  }

  /**
   * 在侧边栏webview中显示
   */
  public showInWebview(webview: vscode.Webview, webviewView?: vscode.WebviewView) {
    // 关闭主面板
    if (this._panel) {
      console.log('🔄 关闭主面板，因为侧边栏正在打开')
      this._panel.dispose()
      this._panel = undefined
    }

    this._webview = webview
    this._webviewView = webviewView
    this._webview.html = this._getHtmlForWebview()
    this._setupWebviewMessageHandler(this._webview)
  }

  /**
   * 重新初始化webview
   */
  public reinitializeWebview() {
    if (this._webview) {
      this._setupWebviewMessageHandler(this._webview)
    }
  }

  /**
   * 设置webview消息处理器
   */
  private _setupWebviewMessageHandler(webview: vscode.Webview) {
    if (this._messageHandlerDisposable) {
      this._messageHandlerDisposable.dispose()
    }

    this._messageHandlerDisposable = webview.onDidReceiveMessage(
      message => this._handleWebviewMessage(message),
      null,
      this._disposables
    )
  }

  /**
   * 处理来自webview的消息
   */
  private _handleWebviewMessage(message: any) {
    switch (message.type) {
      case 'webviewReady':
        if (!this._isWebviewReady) {
          this._isWebviewReady = true
          this._sendReadyMessage()
        }
        break
      case 'sendMessage':
        this._sendMessageToClaude(message.data.content)
        break
      case 'newSession':
        this._newSession()
        break
      case 'clearChat':
        this._clearChat()
        break
      case 'stopRequest':
        this._stopClaudeProcess()
        break
      case 'requestReload':
        // 开发模式下支持热重载
        if (this._context.extensionMode === vscode.ExtensionMode.Development) {
          console.log('🔄 开发模式：重新加载webview')
          this._reloadWebview()
        }
        break
      case 'openFile':
        console.log('📨 收到openFile消息:', message)
        this._openFile(message.filePath)
        break
      default:
        console.log('🔍 未知消息类型:', message.type)
    }
  }

  /**
   * 打开文件
   */
  private async _openFile(filePath: string) {
    try {
      console.log('📂 打开文件:', filePath)

      // 解析文件路径，支持相对路径和绝对路径
      let uri: vscode.Uri
      if (path.isAbsolute(filePath)) {
        uri = vscode.Uri.file(filePath)
      } else {
        // 相对于工作区根目录
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0]
        if (workspaceFolder) {
          uri = vscode.Uri.joinPath(workspaceFolder.uri, filePath)
        } else {
          uri = vscode.Uri.file(filePath)
        }
      }

      // 打开文件
      await vscode.window.showTextDocument(uri)
    } catch (error) {
      console.error('❌ 打开文件失败:', error)
      vscode.window.showErrorMessage(`无法打开文件: ${filePath}`)
    }
  }

  /**
   * 发送就绪消息
   */
  private _sendReadyMessage() {
    console.log('发送就绪消息')
    this._postMessage({
      type: 'message',
      data: {
        content: '🤖 Claude Chat 已就绪！请输入您的问题...',
        sessionId: this._claudeService.getCurrentSessionId()
      }
    })
  }

  /**
   * 发送消息给Claude
   */
  private async _sendMessageToClaude(message: string) {
    if (!message.trim()) return

    try {
      await this._claudeService.sendMessage(message)
    } catch (error) {
      console.error('发送消息失败:', error)
      this._postMessage({
        type: 'error',
        data: { message: error instanceof Error ? error.message : '发送消息失败' }
      })
    }
  }

  /**
   * 开始新会话
   */
  private _newSession() {
    this._claudeService.newSession()
    this._claudeService.resetUsageStats() // 重置统计数据
    this._currentStreamingMessage = ''

    this._postMessage({
      type: 'sessionInfo',
      data: { sessionId: undefined }
    })
  }

  /**
   * 清空聊天
   */
  private _clearChat() {
    // 发送清空消息到webview
    // webview会处理UI清空
  }

  /**
   * 发送消息到webview
   */
  private _postMessage(message: any) {
    console.log('📤 扩展端发送消息到webview:', message.type, message.data)
    
    if (this._panel) {
      console.log('📤 通过主面板发送消息')
      this._panel.webview.postMessage(message)
    } else if (this._webview) {
      console.log('📤 通过侧边栏发送消息')
      this._webview.postMessage(message)
    } else {
      console.log('⚠️ 没有可用的webview实例')
    }
  }

  /**
   * 关闭侧边栏
   */
  private _closeSidebar() {
    if (this._webviewView) {
      vscode.commands.executeCommand('workbench.view.explorer')
    }
  }

  /**
   * 获取webview HTML内容
   */
  private _getHtmlForWebview(): string {
    const webviewUri = this._panel?.webview || this._webview
    if (!webviewUri) return ''

    const scriptUri = webviewUri.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'out', 'webview', 'webview.iife.js')
    )
    const styleUri = webviewUri.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'out', 'webview', 'style.css')
    )

    return `<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Claude Code</title>
        <link href="${styleUri}" rel="stylesheet">
    </head>
    <body>
        <div id="app"></div>
        <script src="${scriptUri}"></script>
    </body>
    </html>`
  }

  /**
   * 重新加载webview（开发模式）
   */
  private _reloadWebview() {
    if (this._panel) {
      this._panel.webview.html = this._getHtmlForWebview()
    } else if (this._webview) {
      this._webview.html = this._getHtmlForWebview()
    }
  }

  /**
   * 停止Claude进程
   */
  private _stopClaudeProcess() {
    console.log('🛑 停止Claude进程')
    this._claudeService.stopCurrentProcess()

    // 发送停止状态到webview
    this._postMessage({
      type: 'setProcessing',
      data: {
        isProcessing: false
      }
    })
  }

  /**
   * 清理资源
   */
  public dispose() {
    this._claudeService.dispose()

    while (this._disposables.length) {
      const disposable = this._disposables.pop()
      if (disposable) {
        disposable.dispose()
      }
    }
  }
}
