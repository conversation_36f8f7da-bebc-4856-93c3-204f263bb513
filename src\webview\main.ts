import { createApp } from 'vue'
import App from './App.vue'
import './styles/main.css'

/**
 * 创建Vue应用实例
 */
const app = createApp(App)

/**
 * 全局错误处理
 */
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue应用错误:', err)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)
  
  // 可以在这里添加错误上报逻辑
  // 例如发送错误信息到VSCode扩展
  try {
    const vscode = window.acquireVsCodeApi()
    vscode.postMessage({
      type: 'error',
      data: {
        message: err instanceof Error ? err.message : String(err),
        stack: err instanceof Error ? err.stack : undefined,
        info
      }
    })
  } catch (e) {
    console.error('无法发送错误信息到VSCode:', e)
  }
}

/**
 * 全局警告处理（开发环境）
 */
if (process.env.NODE_ENV === 'development') {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue警告:', msg)
    console.warn('组件追踪:', trace)
  }
}

/**
 * 挂载应用
 */
app.mount('#app')

/**
 * 开发环境热重载支持
 */
if (process.env.NODE_ENV === 'development') {
  // 启用Vue DevTools
  app.config.devtools = true

  // 热重载时保持状态
  if (import.meta.hot) {
    import.meta.hot.accept()
  }

  // 开发模式下添加快捷键支持
  document.addEventListener('keydown', (e) => {
    // Ctrl+R 或 Cmd+R 重新加载
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
      e.preventDefault()
      console.log('🔄 开发模式：请求重新加载')
      try {
        const vscode = window.acquireVsCodeApi()
        vscode.postMessage({
          type: 'requestReload'
        })
      } catch (error) {
        console.log('重新加载请求失败:', error)
        location.reload()
      }
    }
  })
}

/**
 * 导出应用实例（用于调试）
 */
if (process.env.NODE_ENV === 'development') {
  (window as any).__VUE_APP__ = app
}

console.log('🚀 Claude Code Vue应用已启动')
