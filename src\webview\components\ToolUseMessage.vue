<template>
  <div class="tool-message">
    <!-- 工具调用头部 -->
    <div class="tool-header" @click="toggleExpanded" :class="{ 'tool-loading': isLoading }">
      <!-- 工具图标占位 -->
      <div class="tool-icon-placeholder"></div>
      <div class="tool-info">
        <div class="tool-name-row">
          <span class="tool-name">{{ displayToolName }}</span>
          <!-- 工具描述信息 -->
          <span
            v-if="toolDescription"
            class="tool-description"
            :class="{ 'tool-file-path': isFilePath }"
            @click.stop="handleDescriptionClick"
          >
            {{ toolDescription }}
          </span>
        </div>
      </div>
      
      <!-- 进度条移到最后 -->
      <div class="tool-progress-container">
        <!-- 进度条指示器 -->
        <div class="progress-bar">
          <div
            :class="[
              'progress-fill',
              isLoading ? 'progress-loading' : '',
              toolResult?.isError ? 'progress-error' : '',
              toolResult && !toolResult.isError ? 'progress-success' : '',
              !isLoading && !toolResult ? 'progress-ready' : ''
            ]"
          ></div>
        </div>
        
        <!-- 状态文字 -->
        <span class="progress-label">
          <span v-if="isLoading">执行中</span>
          <span v-else-if="toolResult?.isError">错误</span>
          <span v-else-if="toolResult">完成</span>
          <span v-else>就绪</span>
        </span>
      </div>
      
      <div class="expand-icon" :class="{ expanded: isExpanded }">
        <i class="ri-arrow-down-s-line"></i>
      </div>
    </div>

    <!-- 工具参数内容 -->
    <div v-if="isExpanded" class="tool-content">
      <div v-if="toolInput" class="tool-input">
        <div class="tool-section-title">参数:</div>
        <div class="tool-input-content">
          <pre v-if="formattedInput" class="tool-input-text">{{ formattedInput }}</pre>
          <div v-else class="tool-input-empty">无参数</div>
        </div>
      </div>
      
      <!-- 工具结果 -->
      <div v-if="toolResult" class="tool-result">
        <div 
          :class="[
            'tool-result-content',
            { 'tool-result-error': toolResult.isError }
          ]"
        >
          <pre class="tool-result-text">{{ toolResult.content }}</pre>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="tool-loading">
        <div class="loading-dots">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <span>执行中...</span>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { ToolResultData } from '../../types'
import { openFile } from '../utils/vscode'

// Props
interface Props {
  toolInfo: string
  toolInput?: any
  rawInput?: any
  toolName: string
  toolUseId?: string
  timestamp: number
  toolResult?: ToolResultData
  isLoading?: boolean
}

const props = defineProps<Props>()

// 展开状态 - 默认展开以显示工具调用过程
const isExpanded = ref(true)
// 用户是否手动操作过展开状态
const userInteracted = ref(false)

/**
 * 切换展开状态
 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  userInteracted.value = true // 标记用户已手动操作
}

/**
 * 监听加载状态和工具结果，智能控制展开状态
 */
watch(() => props.isLoading, (newLoading) => {
  if (newLoading) {
    // 工具开始执行时展开
    isExpanded.value = true
  }
}, { immediate: true })

/**
 * 监听工具结果，完成后自动关闭（除非有错误或用户手动操作过）
 */
watch(() => props.toolResult, (newResult) => {
  if (newResult && !props.isLoading && !newResult.isError && !userInteracted.value) {
    // 成功完成后立即自动关闭
    isExpanded.value = false
  }
}, { immediate: true })

/**
 * 获取工具对应的emoji
 */
const toolEmoji = computed(() => {
  const emojiMap: Record<string, string> = {
    'WebSearch': '🔍',
    'Edit': '✏️',
    'str-replace-editor': '✏️',
    'Read': '📖',
    'view': '👀',
    'Write': '📝',
    'save-file': '💾',
    'MultiEdit': '📝',
    'launch-process': '⚡',
    'codebase-retrieval': '🔎',
    'TodoWrite': '📋',
    'Bash': '💻',
    'Glob': '🔍',
    'Grep': '🔍',
    'LS': '�'
  }

  return emojiMap[props.toolName] || '🔧'
})

/**
 * 格式化文件路径显示
 */
const formatFilePath = (filePath: string): string => {
  if (!filePath) return ''

  // 定义工作区根目录路径
  const workspaceRoot = 'e:\\work\\ai-coding-config'

  // 如果是绝对路径且包含工作区根目录，则转换为相对路径
  if (filePath.startsWith(workspaceRoot)) {
    const relativePath = filePath.substring(workspaceRoot.length)
    // 移除开头的路径分隔符
    return relativePath.replace(/^[\\\/]/, '')
  }

  // 如果是其他绝对路径，只显示文件名
  if (filePath.includes('\\') || filePath.includes('/')) {
    const parts = filePath.split(/[\\\/]/)
    return parts[parts.length - 1]
  }

  // 已经是相对路径或文件名，直接返回
  return filePath
}

/**
 * 获取工具描述信息
 */
const toolDescription = computed(() => {
  if (!props.rawInput) return ''

  // 根据不同工具类型返回相应的描述
  switch (props.toolName) {
    case 'WebSearch':
      // WebSearch: 显示搜索关键词
      if (typeof props.rawInput === 'object' && props.rawInput.query) {
        return props.rawInput.query
      }
      break

    case 'Edit':
    case 'str-replace-editor':
      // 编辑工具: 显示格式化的文件路径
      if (typeof props.rawInput === 'object') {
        const filePath = props.rawInput.path || props.rawInput.file_path
        return formatFilePath(filePath)
      }
      break

    case 'Read':
    case 'view':
      // 读取工具: 显示格式化的文件路径
      if (typeof props.rawInput === 'object') {
        const filePath = props.rawInput.path || props.rawInput.file_path
        return formatFilePath(filePath)
      }
      break

    case 'Write':
    case 'save-file':
      // 写入工具: 显示格式化的文件路径
      if (typeof props.rawInput === 'object') {
        const filePath = props.rawInput.path || props.rawInput.file_path
        return formatFilePath(filePath)
      }
      break

    case 'launch-process':
    case 'Bash':
      // 命令执行: 显示命令
      if (typeof props.rawInput === 'object' && props.rawInput.command) {
        return props.rawInput.command
      }
      break

    case 'MultiEdit':
      // 批量编辑: 显示格式化的文件路径
      if (typeof props.rawInput === 'object') {
        const filePath = props.rawInput.path || props.rawInput.file_path
        return formatFilePath(filePath)
      }
      break

    case 'Glob':
    case 'Grep':
      // 搜索工具: 显示搜索模式
      if (typeof props.rawInput === 'object') {
        return props.rawInput.pattern || props.rawInput.query || props.rawInput.glob
      }
      break

    case 'LS':
      // 列表工具: 显示格式化的路径
      if (typeof props.rawInput === 'object') {
        const dirPath = props.rawInput.path || props.rawInput.directory
        return formatFilePath(dirPath)
      }
      break

    case 'WebFetch':
      // 网页抓取: 显示URL
      if (typeof props.rawInput === 'object' && props.rawInput.url) {
        return props.rawInput.url
      }
      break

    case 'TodoWrite':
      // 待办事项: 显示操作描述
      if (typeof props.rawInput === 'object' && props.rawInput.todos) {
        const todoCount = Array.isArray(props.rawInput.todos) ? props.rawInput.todos.length : 1
        return `${todoCount} 个待办事项`
      }
      break

    default:
      // 其他工具: 可以根据需要添加更多逻辑
      return ''
  }

  return ''
})

/**
 * 获取原始文件路径（用于点击打开）
 */
const originalFilePath = computed(() => {
  if (!props.rawInput || typeof props.rawInput !== 'object') return ''

  // 文件相关的工具类型
  const fileTools = ['Edit', 'str-replace-editor', 'Read', 'view', 'Write', 'save-file', 'MultiEdit']
  if (!fileTools.includes(props.toolName)) return ''

  return props.rawInput.path || props.rawInput.file_path || ''
})

/**
 * 判断描述信息是否为文件路径
 */
const isFilePath = computed(() => {
  return !!originalFilePath.value
})

/**
 * 处理描述信息点击事件
 */
const handleDescriptionClick = () => {
  console.log('🖱️ 点击事件触发')
  console.log('📁 isFilePath:', isFilePath.value)
  console.log('📄 显示路径:', toolDescription.value)
  console.log('📄 原始路径:', originalFilePath.value)
  console.log('🔧 toolName:', props.toolName)

  if (isFilePath.value && originalFilePath.value) {
    console.log('🔗 准备打开文件路径:', originalFilePath.value)

    // 使用原始完整路径打开文件
    const success = openFile(originalFilePath.value)
    if (success) {
      console.log('✅ 文件打开请求已发送')
    } else {
      console.error('❌ 文件打开请求失败')
    }
  } else {
    console.log('⚠️ 不是文件路径或路径为空，跳过处理')
  }
}

/**
 * 显示的工具名称
 */
const displayToolName = computed(() => {
  let name = props.toolInfo.replace('🔧 Executing: ', '')

  // 替换为更友好的名称
  const nameMap: Record<string, string> = {
    'TodoWrite': '更新待办',
    'Edit': '编辑文件',
    'Write': '写入文件',
    'Read': '读取文件',
    'MultiEdit': '批量编辑',
    'str-replace-editor': '编辑器',
    'save-file': '保存文件',
    'view': '查看文件',
    'codebase-retrieval': '代码检索',
    'web-search': '网络搜索',
    'WebSearch': '网络搜索',
    'launch-process': '执行命令',
    'LS': '列出文件',
    'Bash': '执行命令',
    'Glob': '文件匹配',
    'Grep': '文本搜索'
  }

  return nameMap[name] || name
})

/**
 * 格式化输入参数
 */
const formattedInput = computed(() => {
  if (!props.toolInput && !props.rawInput) return ''
  
  const input = props.rawInput || props.toolInput
  
  if (typeof input === 'string') {
    return input
  }
  
  if (typeof input === 'object') {
    try {
      return JSON.stringify(input, null, 2)
    } catch {
      return String(input)
    }
  }
  
  return String(input)
})

/**
 * 格式化时间显示
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.tool-message {
  margin: 4px 0;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  background: var(--vscode-editor-background);
  overflow: hidden;
}

.tool-header {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  cursor: pointer;
  background: var(--vscode-sideBar-background);
  border-bottom: 1px solid var(--vscode-panel-border);
  transition: background-color 0.2s ease;
}

.tool-header:hover {
  background: var(--vscode-list-hoverBackground);
}

.tool-header.tool-loading {
  background: rgba(255, 193, 7, 0.1);
  border-bottom-color: rgba(255, 193, 7, 0.3);
}

.tool-icon-placeholder {
  width: 20px;
  margin-right: 6px;
  flex-shrink: 0;
}

.tool-progress-container {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 100px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: var(--vscode-progressBar-background);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  width: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
  position: relative;
}

.progress-ready {
  background: linear-gradient(90deg,
    var(--vscode-charts-blue) 0%,
    var(--vscode-charts-blue) 30%,
    transparent 30%);
  width: 30%;
}

.progress-loading {
  background: linear-gradient(90deg,
    var(--vscode-charts-orange),
    var(--vscode-charts-yellow));
  animation: progress-loading 2s ease-in-out infinite;
}

.progress-success {
  background: var(--vscode-charts-green);
  width: 100%;
}

.progress-error {
  background: var(--vscode-charts-red);
  width: 100%;
}

.progress-label {
  font-size: 10px;
  font-weight: 600;
  color: var(--vscode-foreground);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

@keyframes progress-loading {
  0% {
    transform: translateX(-100%);
    width: 30%;
  }
  50% {
    transform: translateX(0%);
    width: 70%;
  }
  100% {
    transform: translateX(100%);
    width: 30%;
  }
}

/* 进度条光效动画 */
.progress-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.tool-info {
  flex: 1;
}

.tool-name-row {
  display: flex;
  align-items: center;
  gap: 0;
}

.tool-name {
  font-weight: 600;
  color: var(--vscode-foreground);
  font-size: 12px;
  line-height: 1.3;
  flex-shrink: 0;
}

.tool-description {
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  margin-left: 6px;
  opacity: 0.8;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1;
}

.tool-file-path {
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  color: var(--vscode-textLink-foreground);
  cursor: pointer;
  opacity: 1;
  font-weight: 500;
  padding: 1px 2px;
  text-decoration: underline;
  max-width: 300px;
  display: inline;
  border: 1px solid transparent;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.tool-file-path:hover {
  color: var(--vscode-textLink-activeForeground);
  border-color: var(--vscode-focusBorder);
}

.tool-timestamp {
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  white-space: nowrap;
  flex-shrink: 0;
  margin-left: auto;
}



.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}



.expand-icon {
  transition: transform 0.2s ease;
  color: var(--vscode-descriptionForeground);
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.tool-content {
  padding: 8px 10px;
  background: var(--vscode-editor-background);
}

.tool-section-title {
  font-size: 11px;
  font-weight: 600;
  color: var(--vscode-foreground);
  margin-bottom: 4px;
  opacity: 0.8;
}

.tool-input {
  margin-bottom: 8px;
}

.tool-input-content {
  background: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.tool-input-text {
  margin: 0;
  padding: 6px 8px;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  color: var(--vscode-editor-foreground);
  background: transparent;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 150px;
  overflow-y: auto;
  line-height: 1.4;
}

.tool-input-empty {
  padding: 6px 8px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

.tool-result {
  margin-top: 8px;
}

.tool-result-content {
  background: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  overflow: hidden;
}

.tool-result-content.tool-result-error {
  border-color: var(--vscode-errorForeground);
  background: rgba(255, 69, 58, 0.1);
}

.tool-result-text {
  margin: 0;
  padding: 6px 8px;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  color: var(--vscode-editor-foreground);
  background: transparent;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.4;
}

.tool-result-error .tool-result-text {
  color: var(--vscode-errorForeground);
}

.tool-loading {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  color: var(--vscode-descriptionForeground);
  font-size: 11px;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--vscode-progressBar-background);
  animation: loading-pulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes loading-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
