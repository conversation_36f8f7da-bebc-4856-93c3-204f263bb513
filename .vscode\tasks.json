{"version": "2.0.0", "tasks": [{"type": "typescript", "tsconfig": "tsconfig.json", "problemMatcher": "$tsc", "group": "build", "label": "tsc:build", "detail": "编译TypeScript源码"}, {"type": "typescript", "tsconfig": "tsconfig.json", "option": "watch", "problemMatcher": "$tsc-watch", "group": "build", "label": "tsc:watch", "detail": "监视模式编译TypeScript"}, {"type": "shell", "command": "yarn", "args": ["watch"], "group": "build", "label": "dev:watch", "detail": "开发模式 - 自动监听并编译所有代码变更", "isBackground": true, "problemMatcher": [{"owner": "typescript", "pattern": {"regexp": "^([^\\s].*):(?:(\\d+):)(?:(\\d+):)?\\s+(error|warning|info)\\s+(TS\\d+)\\s*:\\s*(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "code": 5, "message": 6}, "background": {"activeOnStart": true, "beginsPattern": "^\\s*\\d{1,2}:\\d{2}:\\d{2} (AM|PM) - File change detected\\. Starting incremental compilation\\.\\.\\.$", "endsPattern": "^\\s*\\d{1,2}:\\d{2}:\\d{2} (AM|PM) - (Compilation complete\\. Watching for file changes\\.|Found \\d+ errors?\\. Watching for file changes\\.)$"}}, {"owner": "vite", "fileLocation": "relative", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^\\s*(vite|Local:|✓ built in)", "endsPattern": "^\\s*(✓ built in|ready in|Local:|Network:)"}}], "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "runOptions": {"runOn": "default"}}, {"type": "shell", "command": "yarn", "args": ["watch:webview"], "group": "build", "label": "watch:production", "detail": "生产模式监视 - 构建生产版本", "isBackground": true, "problemMatcher": [{"owner": "vite", "fileLocation": "relative", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^\\s*vite", "endsPattern": "^\\s*✓ built in"}}]}, {"type": "shell", "command": "yarn", "args": ["compile"], "group": "build", "label": "build:all", "detail": "完整构建项目（包括webview和TypeScript）", "problemMatcher": []}, {"type": "shell", "command": "yarn", "args": ["build:webview"], "group": "build", "label": "build:webview", "detail": "构建webview前端资源", "problemMatcher": []}]}