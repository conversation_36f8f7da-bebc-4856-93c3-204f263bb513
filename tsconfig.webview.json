{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/webview/*"]}, "lib": ["ES2020", "DOM", "DOM.Iterable"], "types": ["vite/client"]}, "include": ["src/webview/**/*.ts", "src/webview/**/*.tsx", "src/webview/**/*.vue", "src/types/**/*.ts"], "exclude": ["node_modules", "out"]}