{"name": "claude-code-chat-permissions-mcp", "version": "1.0.0", "main": "dist/mcp-permissions.js", "scripts": {"start": "tsc && node dist/mcp-permissions.js", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^24.0.13", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1", "zod": "^3.25.76"}}