# Claude Chat

基于 Vue3 + Vite + TailwindCSS 重构的 VSCode 扩展，提供 Claude AI 聊天功能。

## 🚀 特性

- 🎯 **简洁聊天界面** - 专注于核心对话功能
- ⚡ **Vue3 + Vite** - 现代化前端技术栈
- 🎨 **TailwindCSS** - 响应式设计，适配 VSCode 主题
- 🔥 **热重载开发** - 快速开发调试体验
- 📱 **双模式显示** - 支持主面板和侧边栏
- 🌙 **主题适配** - 自动适配 VSCode 明暗主题

## 📁 项目结构

```
claude-code/
├── src/
│   ├── extension.ts              # VSCode扩展入口
│   ├── webview/                  # Vue3前端代码
│   │   ├── main.ts              # Vue应用入口
│   │   ├── App.vue              # 根组件
│   │   ├── components/          # 组件目录
│   │   │   ├── ChatContainer.vue # 聊天容器
│   │   │   ├── MessageItem.vue   # 消息项
│   │   │   └── InputBox.vue      # 输入框
│   │   ├── composables/         # 组合式函数
│   │   │   └── useChat.ts       # 聊天逻辑
│   │   └── styles/              # 样式文件
│   │       └── main.css         # 主样式
│   └── types/                   # 类型定义
│       └── index.ts
├── package.json                 # 项目配置
├── vite.config.ts              # Vite配置
├── tailwind.config.js          # TailwindCSS配置
├── tsconfig.json               # TypeScript配置
└── README.md                   # 项目说明
```

## 🛠️ 开发

### 安装依赖

```bash
yarn install
```

### 开发模式

```bash
# 启动Vue3前端热重载
yarn dev

# 监听TypeScript编译
yarn watch

# 同时启动前端和后端监听
yarn watch
```

### 构建

```bash
# 构建生产版本
yarn compile
```

### 调试

1. 按 `F5` 启动扩展开发主机
2. 在新窗口中按 `Ctrl+Shift+C` 打开聊天面板
3. 或在侧边栏中找到 "Claude Code" 视图

## 📝 使用

### 快捷键

- `Ctrl+Shift+C` (Mac: `Cmd+Shift+C`) - 打开聊天面板

### 功能

- 💬 **基础聊天** - 与 Claude AI 进行对话
- 🆕 **新会话** - 开始新的对话会话
- 🗑️ **清空聊天** - 清除当前聊天记录
- 📱 **响应式界面** - 适配不同窗口大小

## 🎨 界面特色

- **VSCode 主题集成** - 完美融入 VSCode 界面
- **流式消息显示** - 实时显示 AI 回复过程
- **Markdown 支持** - 支持代码块、粗体、斜体等格式
- **消息时间戳** - 智能显示消息时间
- **加载状态** - 清晰的处理状态指示

## 🔧 技术栈

- **前端框架**: Vue 3.3+
- **构建工具**: Vite 4.4+
- **样式框架**: TailwindCSS 3.3+
- **类型支持**: TypeScript 5.2+
- **VSCode API**: 1.74+

## 📦 构建产物

- `out/extension.js` - VSCode 扩展主文件
- `out/webview/webview.js` - Vue3 前端应用
- `out/webview/webview.css` - 样式文件

## 🚧 开发计划

- [ ] 集成真实的 Claude API
- [ ] 添加消息历史记录
- [ ] 支持文件上传和引用
- [ ] 添加代码高亮
- [ ] 支持更多 Markdown 语法

## 📄 许可证

MIT License

---

基于 [claude-code-chat-老版本](./claude-code-chat-老版本) 重构开发
