<template>
  <div class="tooltip-wrapper">
    <!-- 触发器插槽 -->
    <div 
      class="tooltip-trigger"
      @mouseenter="showTooltip = true"
      @mouseleave="showTooltip = false"
    >
      <slot />
    </div>
    
    <!-- Tooltip 内容 -->
    <Teleport to="body">
      <div 
        v-if="showTooltip && computedContent"
        ref="tooltipRef"
        class="custom-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-content">
          <!-- 主要文本 -->
          <div class="tooltip-text">{{ computedContent }}</div>
          
          <!-- 快捷键提示 -->
          <div v-if="shortcut" class="tooltip-shortcut">
            <template v-for="(key, index) in shortcutKeys" :key="index">
              <span v-if="index > 0" class="shortcut-separator">+</span>
              <kbd>{{ key }}</kbd>
            </template>
          </div>
        </div>
        <div class="tooltip-arrow" :style="arrowStyle"></div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'

/** Tooltip 位置 */
type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end'

interface Props {
  /** 提示内容 */
  content?: string
  /** 禁用状态下的提示内容 */
  disabledContent?: string
  /** 是否禁用状态 */
  disabled?: boolean
  /** 快捷键提示 */
  shortcut?: string
  /** 位置 */
  placement?: TooltipPlacement
  /** 偏移距离 */
  offset?: number
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  disabledContent: '',
  disabled: false,
  shortcut: '',
  placement: 'top',
  offset: 8
})

// 响应式变量
const showTooltip = ref(false)
const tooltipRef = ref<HTMLElement>()
const tooltipStyle = ref({})
const arrowStyle = ref({})

// 计算属性
const computedContent = computed(() => {
  return props.disabled ? props.disabledContent : props.content
})

const shortcutKeys = computed(() => {
  return props.shortcut ? props.shortcut.split('+').map(key => key.trim()) : []
})

/**
 * 计算 Tooltip 位置
 */
const calculatePosition = () => {
  nextTick(() => {
    if (!tooltipRef.value) return

    const trigger = tooltipRef.value.parentElement?.querySelector('.tooltip-trigger')
    if (!trigger) return

    const triggerRect = trigger.getBoundingClientRect()
    const tooltipRect = tooltipRef.value.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    let top = 0
    let left = 0
    let arrowPosition = {}

    switch (props.placement) {
      case 'top':
      case 'top-start':
      case 'top-end':
        top = triggerRect.top - tooltipRect.height - props.offset
        if (props.placement === 'top-start') {
          left = triggerRect.left
        } else if (props.placement === 'top-end') {
          left = triggerRect.right - tooltipRect.width
        } else {
          left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        }
        arrowPosition = {
          top: '100%',
          left: props.placement === 'top-end' ? 'auto' : '50%',
          right: props.placement === 'top-end' ? '12px' : 'auto',
          transform: props.placement === 'top-end' ? 'none' : 'translateX(-50%)',
          borderTop: '5px solid var(--tooltip-bg)',
          borderLeft: '5px solid transparent',
          borderRight: '5px solid transparent',
          borderBottom: 'none'
        }
        break

      case 'bottom':
      case 'bottom-start':
      case 'bottom-end':
        top = triggerRect.bottom + props.offset
        if (props.placement === 'bottom-start') {
          left = triggerRect.left
        } else if (props.placement === 'bottom-end') {
          left = triggerRect.right - tooltipRect.width
        } else {
          left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        }
        arrowPosition = {
          bottom: '100%',
          left: props.placement === 'bottom-end' ? 'auto' : '50%',
          right: props.placement === 'bottom-end' ? '12px' : 'auto',
          transform: props.placement === 'bottom-end' ? 'none' : 'translateX(-50%)',
          borderBottom: '5px solid var(--tooltip-bg)',
          borderLeft: '5px solid transparent',
          borderRight: '5px solid transparent',
          borderTop: 'none'
        }
        break

      case 'left':
        top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        left = triggerRect.left - tooltipRect.width - props.offset
        arrowPosition = {
          top: '50%',
          left: '100%',
          transform: 'translateY(-50%)',
          borderLeft: '5px solid var(--tooltip-bg)',
          borderTop: '5px solid transparent',
          borderBottom: '5px solid transparent',
          borderRight: 'none'
        }
        break

      case 'right':
        top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        left = triggerRect.right + props.offset
        arrowPosition = {
          top: '50%',
          right: '100%',
          transform: 'translateY(-50%)',
          borderRight: '5px solid var(--tooltip-bg)',
          borderTop: '5px solid transparent',
          borderBottom: '5px solid transparent',
          borderLeft: 'none'
        }
        break
    }

    // 边界检测和调整
    if (left < 0) left = 8
    if (left + tooltipRect.width > viewportWidth) left = viewportWidth - tooltipRect.width - 8
    if (top < 0) top = 8
    if (top + tooltipRect.height > viewportHeight) top = viewportHeight - tooltipRect.height - 8

    tooltipStyle.value = {
      position: 'fixed',
      top: `${top}px`,
      left: `${left}px`,
      zIndex: 9999
    }

    arrowStyle.value = arrowPosition
  })
}

// 监听显示状态变化
watch(showTooltip, (show) => {
  if (show) {
    calculatePosition()
  }
})
</script>

<style scoped>
.tooltip-wrapper {
  display: inline-block;
}

.tooltip-trigger {
  display: inline-block;
}

/* Tooltip 样式 */
.custom-tooltip {
  position: fixed;
  z-index: 9999;
  opacity: 0;
  transform: translateY(4px);
  animation: tooltipFadeIn 0.2s ease-out forwards;
  --tooltip-bg: #1f2937;
}

.tooltip-content {
  background: var(--tooltip-bg);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: calc(var(--vscode-font-size, 13px) - 2px);
  line-height: 1.4;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  min-width: 80px;
  text-align: center;
}

.tooltip-text {
  margin-bottom: 0;
}

.tooltip-shortcut {
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-size: calc(var(--vscode-font-size, 13px) - 4px);
  opacity: 0.8;
}

.shortcut-separator {
  margin: 0 2px;
  color: rgba(255, 255, 255, 0.6);
}

.tooltip-shortcut kbd {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  padding: 1px 4px;
  font-family: inherit;
  font-size: calc(var(--vscode-font-size, 13px) - 5px);
  color: white;
  min-width: 16px;
  text-align: center;
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 暗色主题适配 */
.dark .custom-tooltip {
  --tooltip-bg: #374151;
}

.dark .tooltip-content {
  border: 1px solid #4b5563;
}

/* 主题色彩变量 */
html[data-vscode-theme-kind="vscode-dark"] .custom-tooltip,
html[data-vscode-theme-name*="dark"] .custom-tooltip {
  --tooltip-bg: #374151;
}

html[data-vscode-theme-kind="vscode-light"] .custom-tooltip,
html[data-vscode-theme-name*="light"] .custom-tooltip {
  --tooltip-bg: #1f2937;
}
</style>
